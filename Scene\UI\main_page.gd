extends CanvasLayer

# 扑克牌游戏UI元素
@onready var bg: TextureRect = $BG
@onready var stop_area: Control = % StopArea
@onready var bt_fold: TextureButton = % BT_Fold
@onready var bt_unfold: TextureButton = % BT_Unfold

# 扑克牌游戏控制按钮
@onready var bt_deal: Button = % BT_Deal
@onready var bt_hit: Button = % BT_Hit
@onready var bt_stand: Button = % BT_Stand
@onready var bt_restart: Button = % BT_Restart

# 扑克牌显示区域
@onready var player_cards_container: HBoxContainer = % PlayerCardsContainer
@onready var dealer_cards_container: HBoxContainer = % DealerCardsContainer

# 游戏信息显示
@onready var player_score_label: Label = % PlayerScoreLabel
@onready var dealer_score_label: Label = % DealerScoreLabel
@onready var game_status_label: Label = % GameStatusLabel

# 扑克牌游戏逻辑变量
var deck: Array = []
var player_cards: Array = []
var dealer_cards: Array = []
var player_score: int = 0
var dealer_score: int = 0
var game_over: bool = false
var dealer_hidden: bool = true

# 扑克牌数据结构
enum Suit { HEARTS, DIAMONDS, CLUBS, SPADES }
enum Rank { ACE = 1, TWO, THREE, FOUR, FIVE, SIX, SEVEN, EIGHT, NINE, TEN, JACK, QUEEN, KING }

class Card:
    var suit: Suit
    var rank: Rank
    var value: int

    func _init(s: Suit, r: Rank):
        suit = s
        rank = r
        # 设置牌的点数值
        if rank >= Rank.JACK:
            value = 10
        elif rank == Rank.ACE:
            value = 11  # A默认为11，后续可调整为1
        else:
            value = rank

var menu_close_pos: Control
var menu_open_pos: Control

func _ready() -> void :
    # 获取菜单位置控制节点
    menu_close_pos = $MenuClosePos
    menu_open_pos = $MenuOpenPos

    # 连接扑克牌游戏按钮信号
    bt_deal.pressed.connect(_on_deal_pressed)
    bt_hit.pressed.connect(_on_hit_pressed)
    bt_stand.pressed.connect(_on_stand_pressed)
    bt_restart.pressed.connect(_on_restart_pressed)

    # 连接菜单折叠按钮
    bt_fold.pressed.connect(_on_bt_fold_pressed)
    bt_unfold.pressed.connect(_on_bt_unfold_pressed)

    # 初始化扑克牌游戏
    initialize_poker_game()

# 扑克牌游戏初始化
func initialize_poker_game():
    create_deck()
    shuffle_deck()
    reset_game()
    update_ui()

# 创建一副牌
func create_deck():
    deck.clear()
    for suit in Suit.values():
        for rank in Rank.values():
            deck.append(Card.new(suit, rank))

# 洗牌
func shuffle_deck():
    for i in range(deck.size()):
        var j = randi() % deck.size()
        var temp = deck[i]
        deck[i] = deck[j]
        deck[j] = temp

# 重置游戏状态
func reset_game():
    player_cards.clear()
    dealer_cards.clear()
    player_score = 0
    dealer_score = 0
    game_over = false
    dealer_hidden = true

    # 清空卡牌显示容器
    for child in player_cards_container.get_children():
        child.queue_free()
    for child in dealer_cards_container.get_children():
        child.queue_free()

    # 重置按钮状态
    bt_deal.disabled = false
    bt_hit.disabled = true
    bt_stand.disabled = true

# 发牌按钮处理
func _on_deal_pressed():
    if deck.size() < 4:  # 确保有足够的牌
        create_deck()
        shuffle_deck()

    # 发两张牌给玩家，两张给庄家
    deal_card_to_player()
    deal_card_to_dealer()
    deal_card_to_player()
    deal_card_to_dealer()

    # 更新按钮状态
    bt_deal.disabled = true
    bt_hit.disabled = false
    bt_stand.disabled = false

    update_ui()
    check_blackjack()

# 要牌按钮处理
func _on_hit_pressed():
    if not game_over:
        deal_card_to_player()
        update_ui()

        if player_score > 21:
            game_over = true
            bt_hit.disabled = true
            bt_stand.disabled = true
            game_status_label.text = "爆牌！庄家获胜"

# 停牌按钮处理
func _on_stand_pressed():
    if not game_over:
        dealer_hidden = false
        dealer_turn()
        determine_winner()
        bt_hit.disabled = true
        bt_stand.disabled = true

# 重新开始按钮处理
func _on_restart_pressed():
    reset_game()
    update_ui()
    game_status_label.text = "点击发牌开始游戏"

# 给玩家发牌
func deal_card_to_player():
    if deck.size() > 0:
        var card = deck.pop_back()
        player_cards.append(card)
        player_score = calculate_score(player_cards)
        add_card_to_container(card, player_cards_container, false)

# 给庄家发牌
func deal_card_to_dealer():
    if deck.size() > 0:
        var card = deck.pop_back()
        dealer_cards.append(card)
        dealer_score = calculate_score(dealer_cards)
        # 第一张牌隐藏
        var hide_card = dealer_hidden and dealer_cards.size() == 1
        add_card_to_container(card, dealer_cards_container, hide_card)

# 计算手牌分数
func calculate_score(cards: Array) -> int:
    var score = 0
    var aces = 0

    for card in cards:
        if card.rank == Rank.ACE:
            aces += 1
            score += 11
        else:
            score += card.value

    # 处理A的值（11或1）
    while score > 21 and aces > 0:
        score -= 10
        aces -= 1

    return score

# 添加卡牌到显示容器
func add_card_to_container(card: Card, container: HBoxContainer, hidden: bool):
    var card_label = Label.new()
    card_label.custom_minimum_size = Vector2(30, 40)
    card_label.add_theme_stylebox_override("normal", create_card_style())

    if hidden:
        card_label.text = "?"
    else:
        card_label.text = get_card_text(card)

    card_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
    card_label.vertical_alignment = VERTICAL_ALIGNMENT_CENTER
    container.add_child(card_label)

# 获取卡牌显示文本
func get_card_text(card: Card) -> String:
    var rank_text = ""
    match card.rank:
        Rank.ACE: rank_text = "A"
        Rank.JACK: rank_text = "J"
        Rank.QUEEN: rank_text = "Q"
        Rank.KING: rank_text = "K"
        _: rank_text = str(card.rank)

    var suit_text = ""
    match card.suit:
        Suit.HEARTS: suit_text = "♥"
        Suit.DIAMONDS: suit_text = "♦"
        Suit.CLUBS: suit_text = "♣"
        Suit.SPADES: suit_text = "♠"

    return rank_text + suit_text

# 创建卡牌样式
func create_card_style() -> StyleBoxFlat:
    var style = StyleBoxFlat.new()
    style.bg_color = Color.WHITE
    style.border_color = Color.BLACK
    style.border_width_left = 2
    style.border_width_right = 2
    style.border_width_top = 2
    style.border_width_bottom = 2
    style.corner_radius_top_left = 4
    style.corner_radius_top_right = 4
    style.corner_radius_bottom_left = 4
    style.corner_radius_bottom_right = 4
    return style

# 庄家回合
func dealer_turn():
    # 显示庄家的隐藏牌
    update_dealer_display()

    # 庄家必须在17以下要牌
    while dealer_score < 17:
        deal_card_to_dealer()
        await get_tree().create_timer(0.5).timeout  # 添加延迟效果

    update_ui()

# 更新庄家显示（显示隐藏的牌）
func update_dealer_display():
    # 清空庄家卡牌显示
    for child in dealer_cards_container.get_children():
        child.queue_free()

    # 重新显示所有庄家卡牌（不隐藏）
    for card in dealer_cards:
        add_card_to_container(card, dealer_cards_container, false)

# 检查是否有21点
func check_blackjack():
    if player_score == 21 and player_cards.size() == 2:
        if dealer_score == 21 and dealer_cards.size() == 2:
            game_status_label.text = "双方都是21点！平局"
        else:
            game_status_label.text = "21点！玩家获胜"
        game_over = true
        bt_hit.disabled = true
        bt_stand.disabled = true

# 判断胜负
func determine_winner():
    game_over = true

    if dealer_score > 21:
        game_status_label.text = "庄家爆牌！玩家获胜"
    elif player_score > dealer_score:
        game_status_label.text = "玩家获胜！"
    elif dealer_score > player_score:
        game_status_label.text = "庄家获胜！"
    else:
        game_status_label.text = "平局！"

# 更新UI显示
func update_ui():
    player_score_label.text = "玩家: " + str(player_score)

    if dealer_hidden and dealer_cards.size() > 0:
        # 庄家有隐藏牌时，只显示可见牌的分数
        var visible_score = 0
        for i in range(1, dealer_cards.size()):
            visible_score += dealer_cards[i].value
        dealer_score_label.text = "庄家: " + str(visible_score) + " + ?"
    else:
        dealer_score_label.text = "庄家: " + str(dealer_score)

# 菜单折叠控制
func _on_bt_fold_pressed() -> void :
    close_menu()

func _on_bt_unfold_pressed() -> void :
    open_menu()

func open_menu():
    bt_unfold.hide()
    bt_fold.show()
    var tween = get_tree().create_tween()
    tween.set_trans(Tween.TRANS_CUBIC)
    tween.set_ease(Tween.EASE_IN_OUT)
    tween.set_parallel(true)
    tween.tween_property(bg, "position", menu_open_pos.position, 0.3)
    stop_area.position = Vector2(-35, 0)
    stop_area.size = Vector2(120, 50)

func close_menu():
    bt_fold.hide()
    bt_unfold.show()
    var tween = get_tree().create_tween()
    tween.set_trans(Tween.TRANS_CUBIC)
    tween.set_ease(Tween.EASE_IN_OUT)
    tween.set_parallel(true)
    tween.tween_property(bg, "position", menu_close_pos.position, 0.3)
    stop_area.position = Vector2(-1, 0)
    stop_area.size = Vector2(86, 50)
