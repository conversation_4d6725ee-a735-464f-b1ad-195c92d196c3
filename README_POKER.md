# 扑克牌游戏 - 主界面改造

## 概述
已成功将主界面的底栏改造成了一个21点扑克牌游戏。原来的商店、设施、收藏等按钮已被移除，整个底栏区域现在是一个完整的扑克牌游戏界面。

## 游戏特性

### 游戏类型
- **21点 (Blackjack)** - 经典的扑克牌游戏

### 游戏规则
1. 目标是让手牌总点数尽可能接近21点，但不能超过
2. A可以算作1点或11点（自动选择最优值）
3. J、Q、K都算作10点
4. 庄家必须在17点以下继续要牌

### 游戏界面

#### 布局
- **庄家区域**: 显示庄家的手牌和分数（第一张牌在游戏中会隐藏）
- **玩家区域**: 显示玩家的手牌和分数
- **控制按钮**: 发牌、要牌、停牌、重新开始
- **游戏状态**: 显示当前游戏状态和结果

#### 控制按钮
- **发牌**: 开始新一轮游戏，给玩家和庄家各发两张牌
- **要牌**: 玩家要一张新牌
- **停牌**: 玩家停止要牌，庄家开始自动要牌
- **重新开始**: 重置游戏状态

#### 菜单控制
- **展开/折叠**: 右侧的箭头按钮可以展开或折叠游戏界面

## 技术实现

### 文件修改
1. **Scene/UI/main_page.gd** - 完全重写，实现扑克牌游戏逻辑
2. **Scene/UI/main_page.tscn** - 重新设计UI布局，移除旧元素，添加扑克牌游戏元素

### 核心功能
- **卡牌系统**: 完整的52张牌，包含花色和点数
- **洗牌算法**: 随机洗牌确保游戏公平性
- **分数计算**: 智能处理A的值（1或11）
- **游戏逻辑**: 完整的21点游戏规则实现
- **UI动画**: 菜单展开/折叠动画效果

### 数据结构
```gdscript
enum Suit { HEARTS, DIAMONDS, CLUBS, SPADES }
enum Rank { ACE = 1, TWO, THREE, FOUR, FIVE, SIX, SEVEN, EIGHT, NINE, TEN, JACK, QUEEN, KING }

class Card:
    var suit: Suit
    var rank: Rank
    var value: int
```

## 如何测试

### 运行游戏
1. 在Godot编辑器中打开项目
2. 运行主场景 (Scene/main_scene.tscn)
3. 游戏界面会出现在屏幕右侧底部

### 测试步骤
1. 点击"发牌"按钮开始游戏
2. 查看玩家和庄家的手牌
3. 使用"要牌"或"停牌"按钮进行游戏
4. 观察游戏结果
5. 点击"重新开始"进行下一轮

### 测试脚本
项目中包含 `test_poker.gd` 文件，可以独立测试扑克牌逻辑：
- 卡牌创建
- 牌组生成
- 分数计算
- 特殊情况处理

## 游戏流程

1. **初始状态**: 显示"点击发牌开始游戏"
2. **发牌阶段**: 玩家和庄家各获得2张牌，庄家第一张牌隐藏
3. **玩家回合**: 玩家可以选择要牌或停牌
4. **庄家回合**: 玩家停牌后，庄家自动要牌直到17点以上
5. **结果判定**: 比较双方点数，显示胜负结果
6. **重新开始**: 可以开始新一轮游戏

## 特殊情况处理

- **21点**: 如果玩家前两张牌就是21点，立即获胜（除非庄家也是21点）
- **爆牌**: 超过21点立即失败
- **A的处理**: 自动选择1或11，确保最优分数
- **平局**: 双方点数相同时为平局

## 界面特点

- **紧凑设计**: 所有元素都在底栏的50像素高度内
- **清晰显示**: 卡牌用文字和符号清楚显示
- **实时更新**: 分数和状态实时更新
- **动画效果**: 庄家要牌时有延迟动画
- **响应式**: 按钮状态根据游戏阶段自动启用/禁用
